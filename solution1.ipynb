{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.168754Z", "iopub.status.busy": "2025-07-16T15:41:28.168465Z", "iopub.status.idle": "2025-07-16T15:41:28.203551Z", "shell.execute_reply": "2025-07-16T15:41:28.202693Z", "shell.execute_reply.started": "2025-07-16T15:41:28.168733Z"}, "trusted": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "\n", "plt.style.use(\"seaborn-v0_8-darkgrid\")\n", "warnings.filterwarnings(\"ignore\")\n", "plt.rc(\"font\",family=\"SimHei\",size=\"15\")  \n", "# import csv\n", "train_df = pd.read_csv(\"train.csv\")\n", "dataset_df = pd.read_csv(\"personality_datasert.csv\")\n", "test_df = pd.read_csv(\"test.csv\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Acquisition and Merging\n", "\n", "This section describes how to acquire the datasets and merge them into a single DataFrame for further analysis and modeling.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.205583Z", "iopub.status.busy": "2025-07-16T15:41:28.205177Z", "iopub.status.idle": "2025-07-16T15:41:28.238190Z", "shell.execute_reply": "2025-07-16T15:41:28.237481Z", "shell.execute_reply.started": "2025-07-16T15:41:28.205562Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 18524 entries, 0 to 18523\n", "Data columns (total 10 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   id                         18524 non-null  int64  \n", " 1   Time_spent_Alone           17334 non-null  float64\n", " 2   Stage_fear                 16631 non-null  object \n", " 3   Social_event_attendance    17344 non-null  float64\n", " 4   Going_outside              17058 non-null  float64\n", " 5   Drained_after_socializing  17375 non-null  object \n", " 6   Friends_circle_size        17470 non-null  float64\n", " 7   Post_frequency             17260 non-null  float64\n", " 8   Personality                18524 non-null  object \n", " 9   match_p                    178 non-null    object \n", "dtypes: float64(5), int64(1), object(4)\n", "memory usage: 1.4+ MB\n"]}], "source": ["\n", "dataset_df = (\n", "    dataset_df\n", "    .rename(columns={'Personality': 'match_p'})\n", "    .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',\n", "                      'Going_outside', 'Drained_after_socializing', \n", "                      'Friends_circle_size', 'Post_frequency'])\n", ")\n", "\n", "merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',\n", "              'Going_outside', 'Drained_after_socializing', \n", "              'Friends_circle_size', 'Post_frequency']\n", "\n", "train_df = train_df.merge(dataset_df, how='left', on=merge_cols)\n", "test_df = test_df.merge(dataset_df, how='left', on=merge_cols)\n", "\n", "train_df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploratory Data Analysis (EDA)\n", "\n", "### Visual Inspection of Training and Test Data\n", "\n", "First, take a look at the training data."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.239273Z", "iopub.status.busy": "2025-07-16T15:41:28.238969Z", "iopub.status.idle": "2025-07-16T15:41:28.285546Z", "shell.execute_reply": "2025-07-16T15:41:28.284586Z", "shell.execute_reply.started": "2025-07-16T15:41:28.239246Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 18524 entries, 0 to 18523\n", "Data columns (total 10 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   id                         18524 non-null  int64  \n", " 1   Time_spent_Alone           17334 non-null  float64\n", " 2   Stage_fear                 16631 non-null  object \n", " 3   Social_event_attendance    17344 non-null  float64\n", " 4   Going_outside              17058 non-null  float64\n", " 5   Drained_after_socializing  17375 non-null  object \n", " 6   Friends_circle_size        17470 non-null  float64\n", " 7   Post_frequency             17260 non-null  float64\n", " 8   Personality                18524 non-null  object \n", " 9   match_p                    178 non-null    object \n", "dtypes: float64(5), int64(1), object(4)\n", "memory usage: 1.4+ MB\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>Time_spent_Alone</th>\n", "      <th>Social_event_attendance</th>\n", "      <th>Going_outside</th>\n", "      <th>Friends_circle_size</th>\n", "      <th>Post_frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>18524.000000</td>\n", "      <td>17334.000000</td>\n", "      <td>17344.000000</td>\n", "      <td>17058.000000</td>\n", "      <td>17470.000000</td>\n", "      <td>17260.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>9261.500000</td>\n", "      <td>3.137764</td>\n", "      <td>5.265106</td>\n", "      <td>4.044319</td>\n", "      <td>7.996737</td>\n", "      <td>4.982097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>5347.562529</td>\n", "      <td>3.003786</td>\n", "      <td>2.753359</td>\n", "      <td>2.062580</td>\n", "      <td>4.223484</td>\n", "      <td>2.879139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4630.750000</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>5.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>9261.500000</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000</td>\n", "      <td>4.000000</td>\n", "      <td>8.000000</td>\n", "      <td>5.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>13892.250000</td>\n", "      <td>4.000000</td>\n", "      <td>8.000000</td>\n", "      <td>6.000000</td>\n", "      <td>12.000000</td>\n", "      <td>7.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>18523.000000</td>\n", "      <td>11.000000</td>\n", "      <td>10.000000</td>\n", "      <td>7.000000</td>\n", "      <td>15.000000</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 id  Time_spent_Alone  Social_event_attendance  Going_outside  \\\n", "count  18524.000000      17334.000000             17344.000000   17058.000000   \n", "mean    9261.500000          3.137764                 5.265106       4.044319   \n", "std     5347.562529          3.003786                 2.753359       2.062580   \n", "min        0.000000          0.000000                 0.000000       0.000000   \n", "25%     4630.750000          1.000000                 3.000000       3.000000   \n", "50%     9261.500000          2.000000                 5.000000       4.000000   \n", "75%    13892.250000          4.000000                 8.000000       6.000000   \n", "max    18523.000000         11.000000                10.000000       7.000000   \n", "\n", "       Friends_circle_size  Post_frequency  \n", "count         17470.000000    17260.000000  \n", "mean              7.996737        4.982097  \n", "std               4.223484        2.879139  \n", "min               0.000000        0.000000  \n", "25%               5.000000        3.000000  \n", "50%               8.000000        5.000000  \n", "75%              12.000000        7.000000  \n", "max              15.000000       10.000000  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["train_df.info()\n", "train_df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It can be observed that all features have some missing values, but the amount of missing data is relatively small.\n", "\n", "Next, use `train_df.head()` to view the values of the first five records."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.286964Z", "iopub.status.busy": "2025-07-16T15:41:28.286651Z", "iopub.status.idle": "2025-07-16T15:41:28.299822Z", "shell.execute_reply": "2025-07-16T15:41:28.298971Z", "shell.execute_reply.started": "2025-07-16T15:41:28.286936Z"}, "trusted": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>Time_spent_Alone</th>\n", "      <th>Stage_fear</th>\n", "      <th>Social_event_attendance</th>\n", "      <th>Going_outside</th>\n", "      <th>Drained_after_socializing</th>\n", "      <th>Friends_circle_size</th>\n", "      <th>Post_frequency</th>\n", "      <th>Personality</th>\n", "      <th>match_p</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>No</td>\n", "      <td>6.0</td>\n", "      <td>4.0</td>\n", "      <td>No</td>\n", "      <td>15.0</td>\n", "      <td>5.0</td>\n", "      <td>Extrovert</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>No</td>\n", "      <td>7.0</td>\n", "      <td>3.0</td>\n", "      <td>No</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>Extrovert</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>6.0</td>\n", "      <td>Yes</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>Introvert</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>No</td>\n", "      <td>7.0</td>\n", "      <td>3.0</td>\n", "      <td>No</td>\n", "      <td>11.0</td>\n", "      <td>5.0</td>\n", "      <td>Extrovert</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>1.0</td>\n", "      <td>No</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>No</td>\n", "      <td>13.0</td>\n", "      <td>NaN</td>\n", "      <td>Extrovert</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  Time_spent_Alone Stage_fear  Social_event_attendance  Going_outside  \\\n", "0   0               0.0         No                      6.0            4.0   \n", "1   1               1.0         No                      7.0            3.0   \n", "2   2               6.0        Yes                      1.0            0.0   \n", "3   3               3.0         No                      7.0            3.0   \n", "4   4               1.0         No                      4.0            4.0   \n", "\n", "  Drained_after_socializing  Friends_circle_size  Post_frequency Personality  \\\n", "0                        No                 15.0             5.0   Extrovert   \n", "1                        No                 10.0             8.0   Extrovert   \n", "2                       NaN                  3.0             0.0   Introvert   \n", "3                        No                 11.0             5.0   Extrovert   \n", "4                        No                 13.0             NaN   Extrovert   \n", "\n", "  match_p  \n", "0     NaN  \n", "1     NaN  \n", "2     NaN  \n", "3     NaN  \n", "4     NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["train_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Correlation Analysis\n", "\n", "Correlation analysis will be conducted using visualizations to observe the relationships between each feature and the target, as well as among the features themselves.\n", "\n", "Correlation measures the strength and direction of the relationship between two or more variables. Based on the correlation values, we can assess how strongly variables are related. The algorithm used for this analysis is the **Pearson Correlation Coefficient**.\n", "\n", "**Pearson Correlation Coefficient:**  \n", "Measures the linear relationship between variables and is suitable for numerical variables.  \n", "- **Positive Correlation:** When one variable increases, the other also increases. For example, there may be a positive correlation between height and weight.  \n", "- **Negative Correlation:** When one variable increases, the other decreases. For example, there may be a negative correlation between temperature and heating usage.  \n", "- **No Correlation:** No clear relationship between the variables.  \n", "\n", "Correlation values typically range from -1 to 1:  \n", "- **1:** Perfect positive correlation  \n", "- **-1:** Perfect negative correlation  \n", "- **0:** No linear correlation  \n", "- **Close to 1 or -1:** Strong correlation  \n", "- **Close to 0:** Weak correlation  \n", "\n", "Correlation analysis is conducted using pandas.  \n", "**Note:** Correlation analysis can only be performed on numerical features."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.302613Z", "iopub.status.busy": "2025-07-16T15:41:28.302240Z", "iopub.status.idle": "2025-07-16T15:41:28.325715Z", "shell.execute_reply": "2025-07-16T15:41:28.324761Z", "shell.execute_reply.started": "2025-07-16T15:41:28.302589Z"}, "trusted": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time_spent_Alone</th>\n", "      <th>Social_event_attendance</th>\n", "      <th>Going_outside</th>\n", "      <th>Friends_circle_size</th>\n", "      <th>Post_frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Time_spent_Alone</th>\n", "      <td>1.000000</td>\n", "      <td>-0.628806</td>\n", "      <td>-0.640884</td>\n", "      <td>-0.598014</td>\n", "      <td>-0.611544</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Social_event_attendance</th>\n", "      <td>-0.628806</td>\n", "      <td>1.000000</td>\n", "      <td>0.585224</td>\n", "      <td>0.566675</td>\n", "      <td>0.566679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Going_outside</th>\n", "      <td>-0.640884</td>\n", "      <td>0.585224</td>\n", "      <td>1.000000</td>\n", "      <td>0.549864</td>\n", "      <td>0.579305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friends_circle_size</th>\n", "      <td>-0.598014</td>\n", "      <td>0.566675</td>\n", "      <td>0.549864</td>\n", "      <td>1.000000</td>\n", "      <td>0.522272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Post_frequency</th>\n", "      <td>-0.611544</td>\n", "      <td>0.566679</td>\n", "      <td>0.579305</td>\n", "      <td>0.522272</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         Time_spent_Alone  Social_event_attendance  \\\n", "Time_spent_Alone                 1.000000                -0.628806   \n", "Social_event_attendance         -0.628806                 1.000000   \n", "Going_outside                   -0.640884                 0.585224   \n", "Friends_circle_size             -0.598014                 0.566675   \n", "Post_frequency                  -0.611544                 0.566679   \n", "\n", "                         Going_outside  Friends_circle_size  Post_frequency  \n", "Time_spent_Alone             -0.640884            -0.598014       -0.611544  \n", "Social_event_attendance       0.585224             0.566675        0.566679  \n", "Going_outside                 1.000000             0.549864        0.579305  \n", "Friends_circle_size           0.549864             1.000000        0.522272  \n", "Post_frequency                0.579305             0.522272        1.000000  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["numeric_df = train_df.select_dtypes(include='number').drop(columns=['id'])\n", "numeric_df.corr()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use Seaborn to display a heatmap for a more intuitive visualization of the correlations between feature values.\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.326962Z", "iopub.status.busy": "2025-07-16T15:41:28.326667Z", "iopub.status.idle": "2025-07-16T15:41:28.633255Z", "shell.execute_reply": "2025-07-16T15:41:28.632382Z", "shell.execute_reply.started": "2025-07-16T15:41:28.326937Z"}, "trusted": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 6))\n", "sns.heatmap(numeric_df.corr(),annot=True,cmap='coolwarm',fmt='.2f', vmin=-1, vmax=1)\n", "plt.title('Heatmap')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the heatmap above, we can observe that **Time_spent_Alone** is negatively correlated with the other features, while the other features are positively correlated with each other. Moreover, the correlation values among the features do not differ significantly.\n", "\n", "**In the missing value imputation section below, features will be grouped based on their correlations. Then, the missing values within each group will be filled using the group's median or mode.**\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Missing Value Imputation\n", "\n", "**In the following missing value imputation process, features are grouped based on the correlation analysis above. Then, missing values within each group are filled using the group's median or mode.**\n", "\n", "When filling missing values, the training and test datasets are first combined and imputed together. This ensures that the imputed values reflect the shared characteristics of both datasets, minimizing discrepancies between training and testing phases.\n", "\n", "First, we examine the missing value status of each feature."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.634583Z", "iopub.status.busy": "2025-07-16T15:41:28.634212Z", "iopub.status.idle": "2025-07-16T15:41:28.647026Z", "shell.execute_reply": "2025-07-16T15:41:28.646202Z", "shell.execute_reply.started": "2025-07-16T15:41:28.634563Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 18524 entries, 0 to 18523\n", "Data columns (total 10 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   id                         18524 non-null  int64  \n", " 1   Time_spent_Alone           17334 non-null  float64\n", " 2   Stage_fear                 16631 non-null  object \n", " 3   Social_event_attendance    17344 non-null  float64\n", " 4   Going_outside              17058 non-null  float64\n", " 5   Drained_after_socializing  17375 non-null  object \n", " 6   Friends_circle_size        17470 non-null  float64\n", " 7   Post_frequency             17260 non-null  float64\n", " 8   Personality                18524 non-null  object \n", " 9   match_p                    178 non-null    object \n", "dtypes: float64(5), int64(1), object(4)\n", "memory usage: 1.4+ MB\n"]}], "source": ["train_df.info()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.648773Z", "iopub.status.busy": "2025-07-16T15:41:28.648188Z", "iopub.status.idle": "2025-07-16T15:41:28.675765Z", "shell.execute_reply": "2025-07-16T15:41:28.674914Z", "shell.execute_reply.started": "2025-07-16T15:41:28.648751Z"}, "trusted": true}, "outputs": [], "source": ["train_ID = train_df['id']\n", "test_ID = test_df['id']\n", "\n", "#Now drop the  'id' colum since it's unnecessary for  the prediction process.\n", "train_df.drop(\"id\", axis = 1, inplace = True)\n", "test_df.drop(\"id\", axis = 1, inplace = True)\n", "\n", "ntrain = train_df.shape[0] \n", "ntest = test_df.shape[0] \n", "y_train = train_df['Personality'].map({'Extrovert': 1, 'Introvert': 0}).values \n", "\n", "all_data = pd.concat((train_df, test_df)).reset_index(drop=True)\n", "all_data.drop(['Personality'], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.677151Z", "iopub.status.busy": "2025-07-16T15:41:28.676865Z", "iopub.status.idle": "2025-07-16T15:41:28.693404Z", "shell.execute_reply": "2025-07-16T15:41:28.692339Z", "shell.execute_reply.started": "2025-07-16T15:41:28.677131Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           23084 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    23122 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**1. Imputing Missing Values for Time_spent_Alone**\n", "\n", "Based on the correlation analysis, we know that **Time_spent_Alone** is inversely related to **Social_event_attendance**.  \n", "Therefore, we divide **Social_event_attendance** into four groups using its 1/4, 1/2 (median), and 3/4 quantiles.  \n", "Then, we fill the missing values of **Time_spent_Alone** within each group accordingly.\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.694539Z", "iopub.status.busy": "2025-07-16T15:41:28.694244Z", "iopub.status.idle": "2025-07-16T15:41:28.718732Z", "shell.execute_reply": "2025-07-16T15:41:28.717768Z", "shell.execute_reply.started": "2025-07-16T15:41:28.694513Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24648 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    23122 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["# 1. Create a new grouping column based on the quartiles of Social_event_attendance\n", "all_data['social_attend_bin'] = pd.qcut(\n", "    all_data['Social_event_attendance'], \n", "    q=[0, 0.25, 0.5, 0.75, 1.0], \n", "    labels=['Q1', 'Q2', 'Q3', 'Q4']\n", ")\n", "\n", "\n", "# 2. Define a function to fill missing values in Time\\_spent\\_Alone with the median within each group\n", "def fill_by_group_median(df, group_col, target_col):\n", "    return df[target_col].fillna(df.groupby(group_col)[target_col].transform('median'))\n", "\n", "# 3.Perform group-wise filling of missing values\n", "all_data['Time_spent_Alone'] = fill_by_group_median(\n", "    all_data, group_col='social_attend_bin', target_col='Time_spent_Alone'\n", ")\n", "\n", "\n", "all_data.drop(columns=['social_attend_bin'], inplace=True)\n", "\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It can be observed that **Time_spent_Alone** still has missing values because the rows with these missing values have both **Time_spent_Alone** and **Social_event_attendance** missing. Therefore, these missing values cannot be imputed based on **Social_event_attendance**.\n", "\n", "Fortunately, **Going_outside** is also correlated with **Time_spent_Alone**, so the same method is applied to fill the missing values of **Time_spent_Alone** using **Going_outside**.\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.720337Z", "iopub.status.busy": "2025-07-16T15:41:28.720037Z", "iopub.status.idle": "2025-07-16T15:41:28.743583Z", "shell.execute_reply": "2025-07-16T15:41:28.742671Z", "shell.execute_reply.started": "2025-07-16T15:41:28.720311Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    23122 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["# 1. Create a new grouping column based on the quartiles of Social_event_attendance\n", "all_data['Going_outside_bin'] = pd.qcut(\n", "    all_data['Going_outside'], \n", "    q=[0, 0.25, 0.5, 0.75, 1.0], \n", "    labels=['Q1', 'Q2', 'Q3', 'Q4']\n", ")\n", "\n", "\n", "# 2. Define a function to fill missing values in Time\\_spent\\_Alone with the median within each group\n", "def fill_by_group_median(df, group_col, target_col):\n", "    return df[target_col].fillna(df.groupby(group_col)[target_col].transform('median'))\n", "\n", "# 3. Perform group-wise filling of missing values\n", "all_data['Time_spent_Alone'] = fill_by_group_median(\n", "    all_data, group_col='Going_outside_bin', target_col='Time_spent_Alone'\n", ")\n", "\n", "\n", "all_data.drop(columns=['Going_outside_bin'], inplace=True)\n", "\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["At this point, the missing values in **Time_spent_Alone** have been fully imputed."]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. **2. Imputing Missing Values for Social_event_attendance**\n", "\n", "Based on the correlation analysis, **Social_event_attendance** has a strong positive correlation with **Going_outside**.  \n", "Therefore, we divide **Going_outside** into four groups according to its 1/4 quantile, median (1/2 quantile), and 3/4 quantile.  \n", "Then, we fill the missing values of **Social_event_attendance** within each group accordingly."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.746659Z", "iopub.status.busy": "2025-07-16T15:41:28.746424Z", "iopub.status.idle": "2025-07-16T15:41:28.769416Z", "shell.execute_reply": "2025-07-16T15:41:28.768676Z", "shell.execute_reply.started": "2025-07-16T15:41:28.746641Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    24637 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["# 1. Create a new grouping column based on the quartiles of Social_event_attendance\n", "all_data['Going_outside_bin'] = pd.qcut(\n", "    all_data['Going_outside'], \n", "    q=[0, 0.25, 0.5, 0.75, 1.0], \n", "    labels=['Q1', 'Q2', 'Q3', 'Q4']\n", ")\n", "\n", "\n", "# 2. Define a function to fill missing values in Time\\_spent\\_Alone with the median within each group\n", "def fill_by_group_median(df, group_col, target_col):\n", "    return df[target_col].fillna(df.groupby(group_col)[target_col].transform('median'))\n", "\n", "# 3. Perform group-wise filling of missing values\n", "all_data['Social_event_attendance'] = fill_by_group_median(\n", "    all_data, group_col='Going_outside_bin', target_col='Social_event_attendance'\n", ")\n", "\n", "\n", "all_data.drop(columns=['Going_outside_bin'], inplace=True)\n", "\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Based on the correlation analysis, **Social_event_attendance** has a strong positive correlation with **Friends_circle_size**.  \n", "Therefore, we divide **Friends_circle_size** into four groups using its 1/4 quantile, median (1/2 quantile), and 3/4 quantile.  \n", "Then, we fill the missing values of **Social_event_attendance** within each group accordingly."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.770613Z", "iopub.status.busy": "2025-07-16T15:41:28.770330Z", "iopub.status.idle": "2025-07-16T15:41:28.797161Z", "shell.execute_reply": "2025-07-16T15:41:28.796274Z", "shell.execute_reply.started": "2025-07-16T15:41:28.770593Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    24696 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["# 1. Create a new grouping column based on the quartiles of Social_event_attendance\n", "all_data['Friends_circle_bin'] = pd.qcut(\n", "    all_data['Friends_circle_size'], \n", "    q=[0, 0.25, 0.5, 0.75, 1.0], \n", "    labels=['Q1', 'Q2', 'Q3', 'Q4']\n", ")\n", "\n", "\n", "# 2. Define a function to fill missing values in Time\\_spent\\_Alone with the median within each group\n", "def fill_by_group_median(df, group_col, target_col):\n", "    return df[target_col].fillna(df.groupby(group_col)[target_col].transform('median'))\n", "\n", "# 3. Perform group-wise filling of missing values\n", "all_data['Social_event_attendance'] = fill_by_group_median(\n", "    all_data, group_col='Friends_circle_bin', target_col='Social_event_attendance'\n", ")\n", "\n", "\n", "all_data.drop(columns=['Friends_circle_bin'], inplace=True)\n", "\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Based on the correlation analysis, **Social_event_attendance** has a strong positive correlation with **Post_frequency**.  \n", "Therefore, we divide **Post_frequency** into four groups using its 1/4 quantile, median (1/2 quantile), and 3/4 quantile.  \n", "Then, we fill the missing values of **Social_event_attendance** within each group accordingly."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.798361Z", "iopub.status.busy": "2025-07-16T15:41:28.798088Z", "iopub.status.idle": "2025-07-16T15:41:28.821931Z", "shell.execute_reply": "2025-07-16T15:41:28.821090Z", "shell.execute_reply.started": "2025-07-16T15:41:28.798341Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    24699 non-null  float64\n", " 3   Going_outside              22767 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        23295 non-null  float64\n", " 6   Post_frequency             23027 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["# 1. Create a new grouping column based on the quartiles of Social_event_attendance\n", "all_data['Post_frequency_bin'] = pd.qcut(\n", "    all_data['Post_frequency'], \n", "    q=[0, 0.25, 0.5, 0.75, 1.0], \n", "    labels=['Q1', 'Q2', 'Q3', 'Q4']\n", ")\n", "\n", "\n", "# 2. Define a function to fill missing values in Time\\_spent\\_Alone with the median within each group\n", "\n", "def fill_by_group_median(df, group_col, target_col):\n", "    return df[target_col].fillna(df.groupby(group_col)[target_col].transform('median'))\n", "\n", "# 3. Perform group-wise filling of missing values\n", "all_data['Social_event_attendance'] = fill_by_group_median(\n", "    all_data, group_col='Post_frequency_bin', target_col='Social_event_attendance'\n", ")\n", "\n", "all_data.drop(columns=['Post_frequency_bin'], inplace=True)\n", "\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**3. Imputing Missing Values for Going_outside, Friends_circle_size, and Post_frequency**\n", "\n", "The missing values of **Going_outside**, **Friends_circle_size**, and **Post_frequency** are filled using the same method as above."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.823096Z", "iopub.status.busy": "2025-07-16T15:41:28.822785Z", "iopub.status.idle": "2025-07-16T15:41:28.869381Z", "shell.execute_reply": "2025-07-16T15:41:28.868527Z", "shell.execute_reply.started": "2025-07-16T15:41:28.823075Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 22208 non-null  object \n", " 2   Social_event_attendance    24699 non-null  float64\n", " 3   Going_outside              24699 non-null  float64\n", " 4   Drained_after_socializing  23118 non-null  object \n", " 5   Friends_circle_size        24699 non-null  float64\n", " 6   Post_frequency             24699 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["def fill_missing_by_quantile_group(df, group_source_col, target_col, quantiles=[0, 0.25, 0.5, 0.75, 1.0], labels=None):\n", "    \"\"\"\n", "        Fill missing values in `target_col` by grouping based on the quantiles of `group_source_col`, and using the median of each group to impute missing values.\n", "        \n", "        **Parameters:**\n", "        - `df` (`pd.DataFrame`): The original dataset\n", "        - `group_source_col` (`str`): The numeric column used for grouping\n", "        - `target_col` (`str`): The target column with missing values to be filled\n", "        - `quantiles` (`list`): Quantile breakpoints for grouping (default is quartiles)\n", "        - `labels` (`list`): Labels for each group (default is auto-generated as Q1/Q2/...)\n", "        \n", "        **Returns:**\n", "        - `pd.DataFrame`: The DataFrame with missing values filled (modifies in place)\n", "\n", "    \"\"\"\n", "    # Automatically Generate Group Labels\n", "    if labels is None:\n", "        labels = [f'Q{i+1}' for i in range(len(quantiles)-1)]\n", "\n", "    temp_bin_col = f'{group_source_col}_bin'\n", "\n", "    # Step 1: Create Grouping Column\n", "    df[temp_bin_col] = pd.qcut(df[group_source_col], q=quantiles, labels=labels)\n", "\n", "    # Step 2: Fill Missing Values Within Groups Using the Median\n", "    df[target_col] = df[target_col].fillna(df.groupby(temp_bin_col)[target_col].transform('median'))\n", "\n", "    # Step 3: Delete Temporary Columns\n", "    df.drop(columns=[temp_bin_col], inplace=True)\n", "\n", "    return df\n", "\n", "all_data = fill_missing_by_quantile_group(\n", "    df=all_data,\n", "    group_source_col='Social_event_attendance',\n", "    target_col='Going_outside'\n", ")\n", "\n", "all_data = fill_missing_by_quantile_group(\n", "    df=all_data,\n", "    group_source_col='Post_frequency',\n", "    target_col='Friends_circle_size'\n", ")\n", "all_data = fill_missing_by_quantile_group(\n", "    df=all_data,\n", "    group_source_col='Going_outside',\n", "    target_col='Friends_circle_size'\n", ")\n", "all_data = fill_missing_by_quantile_group(\n", "    df=all_data,\n", "    group_source_col='Friends_circle_size',\n", "    target_col='Post_frequency'\n", ")\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**4. Imputing Missing Values for Stage_fear and Drained_after_socializing**\n", "\n", "According to the feature analysis, **Stage_fear** and **Drained_after_socializing** have a significant impact on the target variable **Personality**.  \n", "If we fill their missing values using group-based methods as before, it may lead to incorrect imputations.  \n", "Therefore, we assign the value **\"Unknown\"** to the missing entries in these two features.  \n", "This way, when **Stage_fear** or **Drained_after_socializing** is \"Unknown\", the final prediction will rely more on the other features.\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.870575Z", "iopub.status.busy": "2025-07-16T15:41:28.870259Z", "iopub.status.idle": "2025-07-16T15:41:28.889068Z", "shell.execute_reply": "2025-07-16T15:41:28.887874Z", "shell.execute_reply.started": "2025-07-16T15:41:28.870549Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 8 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   Time_spent_Alone           24699 non-null  float64\n", " 1   Stage_fear                 24699 non-null  object \n", " 2   Social_event_attendance    24699 non-null  float64\n", " 3   Going_outside              24699 non-null  float64\n", " 4   Drained_after_socializing  24699 non-null  object \n", " 5   Friends_circle_size        24699 non-null  float64\n", " 6   Post_frequency             24699 non-null  float64\n", " 7   match_p                    236 non-null    object \n", "dtypes: float64(5), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["all_data.fillna({\n", "    'Stage_fear': 'UnKnow',\n", "    'Drained_after_socializing': 'UnKnow'\n", "}, inplace=True)\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Feature Engineering\n", "\n", "Outlier removal is performed to prevent abnormal values from negatively impacting model training.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**1. <PERSON>p<PERSON>**\n", "\n", "Use boxplots to visually inspect the distribution of each feature and identify potential outliers.\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:28.891106Z", "iopub.status.busy": "2025-07-16T15:41:28.890274Z", "iopub.status.idle": "2025-07-16T15:41:29.155800Z", "shell.execute_reply": "2025-07-16T15:41:29.155012Z", "shell.execute_reply.started": "2025-07-16T15:41:28.891074Z"}, "trusted": true}, "outputs": [{"data": {"text/plain": ["<Axes: title={'center': 'Boxplot'}>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["numeric_all_data = all_data.select_dtypes(include='number')\n", "numeric_all_data.plot(kind='box', title='Boxplot', figsize=(12, 5))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.156867Z", "iopub.status.busy": "2025-07-16T15:41:29.156662Z", "iopub.status.idle": "2025-07-16T15:41:29.487094Z", "shell.execute_reply": "2025-07-16T15:41:29.486222Z", "shell.execute_reply.started": "2025-07-16T15:41:29.156851Z"}, "trusted": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n", "findfont: Font family 'SimHei' not found.\n"]}, {"data": {"image/png": "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************************************************************+bN08iIyPlgw8+kDZt2pjlmzdvNsGyBtn9+/eXhg0b2reVnJwsM2bMMIHwyy+/LKNGjTLL4+PjZcKECfL222+bIH7AgAHF2nD//fdLUlKS2a62Q+Xl5cn06dNl6dKl8sYbb8i0adNq/Byh7g/Qi4oKk6Sk9HK3w+A8AAA8k8cG03369HG4/Nprr5V33nlHDh8+LPv27ZNOnTrZ00KUpnRYgbTq1auXXHPNNTJ//nz56KOP5IYbbrC/tmjRIklLSzM9y1YgrWJiYuSee+6R22+/****************************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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 5))\n", "sns.histplot(train_df['Time_spent_Alone'], bins=30, kde=True)\n", "plt.title(\"Time_spent_Alone\")\n", "plt.xlabel(\"Time_spent_Alone\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Handling Categorical Features\n", "\n", "Convert categorical features to one-hot encoding (experiments have shown that one-hot encoding yields better results)."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.488251Z", "iopub.status.busy": "2025-07-16T15:41:29.488022Z", "iopub.status.idle": "2025-07-16T15:41:29.509031Z", "shell.execute_reply": "2025-07-16T15:41:29.508149Z", "shell.execute_reply.started": "2025-07-16T15:41:29.488234Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 24699 entries, 0 to 24698\n", "Data columns (total 13 columns):\n", " #   Column                   Non-Null Count  Dtype  \n", "---  ------                   --------------  -----  \n", " 0   Time_spent_Alone         24699 non-null  float64\n", " 1   Social_event_attendance  24699 non-null  float64\n", " 2   Going_outside            24699 non-null  float64\n", " 3   Friends_circle_size      24699 non-null  float64\n", " 4   Post_frequency           24699 non-null  float64\n", " 5   Stage_No                 24699 non-null  bool   \n", " 6   Stage_UnKnow             24699 non-null  bool   \n", " 7   Stage_Yes                24699 non-null  bool   \n", " 8   Drained_No               24699 non-null  bool   \n", " 9   Drained_UnKnow           24699 non-null  bool   \n", " 10  Drained_Yes              24699 non-null  bool   \n", " 11  match_Extrovert          24699 non-null  bool   \n", " 12  match_Introvert          24699 non-null  bool   \n", "dtypes: bool(8), float64(5)\n", "memory usage: 1.1 MB\n"]}], "source": ["all_data = pd.get_dummies(all_data, columns=['Stage_fear', 'Drained_after_socializing','match_p'], prefix=['Stage', 'Drained','match'])\n", "all_data.info()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.510372Z", "iopub.status.busy": "2025-07-16T15:41:29.509843Z", "iopub.status.idle": "2025-07-16T15:41:29.531312Z", "shell.execute_reply": "2025-07-16T15:41:29.530524Z", "shell.execute_reply.started": "2025-07-16T15:41:29.510350Z"}, "trusted": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time_spent_Alone</th>\n", "      <th>Social_event_attendance</th>\n", "      <th>Going_outside</th>\n", "      <th>Friends_circle_size</th>\n", "      <th>Post_frequency</th>\n", "      <th>Stage_No</th>\n", "      <th>Stage_UnKnow</th>\n", "      <th>Stage_Yes</th>\n", "      <th>Drained_No</th>\n", "      <th>Drained_UnKnow</th>\n", "      <th>Drained_Yes</th>\n", "      <th>match_Extrovert</th>\n", "      <th>match_Introvert</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>4.0</td>\n", "      <td>15.0</td>\n", "      <td>5.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>3.0</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.0</td>\n", "      <td>7.0</td>\n", "      <td>3.0</td>\n", "      <td>11.0</td>\n", "      <td>5.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>13.0</td>\n", "      <td>6.0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Time_spent_Alone  Social_event_attendance  Going_outside  \\\n", "0               0.0                      6.0            4.0   \n", "1               1.0                      7.0            3.0   \n", "2               6.0                      1.0            0.0   \n", "3               3.0                      7.0            3.0   \n", "4               1.0                      4.0            4.0   \n", "\n", "   Friends_circle_size  Post_frequency  Stage_No  Stage_UnKnow  Stage_Yes  \\\n", "0                 15.0             5.0      True         False      False   \n", "1                 10.0             8.0      True         False      False   \n", "2                  3.0             0.0     False         False       True   \n", "3                 11.0             5.0      True         False      False   \n", "4                 13.0             6.0      True         False      False   \n", "\n", "   Drained_No  Drained_UnKnow  Drained_Yes  match_Extrovert  match_Introvert  \n", "0        True           False        False            False            False  \n", "1        True           False        False            False            False  \n", "2       False            True        False            False            False  \n", "3        True           False        False            False            False  \n", "4        True           False        False            False            False  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Training\n", "\n", "Here, we choose six commonly used models for **classification problems**:\n", "\n", "- **L1-regularized Logistic Regression**  \n", "  This model applies L1 regularization to logistic regression, which can perform feature selection and is useful when dealing with high-dimensional data. We use `RobustScaler` for normalization to reduce the impact of outliers.\n", "\n", "- **Elastic Net Logistic Regression**  \n", "  A regularized classification method that combines both L1 and L2 penalties, offering a balance between sparsity and stability.\n", "\n", "- **Support Vector Machine (SVM) with kernel**  \n", "  A powerful classification algorithm that can model non-linear decision boundaries by using kernel functions, making it effective for complex datasets.\n", "\n", "- **Gradient Boosting Classifier**  \n", "  A tree-based ensemble classifier trained using gradient boosting. We use the log-loss (cross-entropy) function, which is suitable for binary or multi-class classification and more robust to outliers.\n", "\n", "- **XGBoost Classifier**  \n", "  An efficient and scalable gradient boosting framework optimized for classification tasks. It supports regularization and handles missing values well.\n", "\n", "- **LightGBM Classifier**  \n", "  A fast and high-performance gradient boosting framework for classification. It is based on decision trees and optimized for speed and memory usage, suitable for large-scale datasets.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Selection and Ensemble Prediction\n", "\n", "In this section, we select different machine learning models and perform ensemble prediction to improve overall performance. By combining the strengths of various models, the ensemble approach aims to achieve more robust and accurate predictions.\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting catboost\n", "  Downloading catboost-1.2.8-cp39-cp39-win_amd64.whl.metadata (1.5 kB)\n", "Collecting lightgbm\n", "  Downloading lightgbm-4.6.0-py3-none-win_amd64.whl.metadata (17 kB)\n", "Collecting graphviz (from catboost)\n", "  Downloading graphviz-0.21-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from catboost) (3.9.4)\n", "Requirement already satisfied: numpy<3.0,>=1.16.0 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from catboost) (2.0.2)\n", "Requirement already satisfied: pandas>=0.24 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from catboost) (2.2.3)\n", "Requirement already satisfied: scipy in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from catboost) (1.13.1)\n", "Collecting plotly (from catboost)\n", "  Downloading plotly-6.2.0-py3-none-any.whl.metadata (8.5 kB)\n", "Requirement already satisfied: six in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from catboost) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from pandas>=0.24->catboost) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from pandas>=0.24->catboost) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from pandas>=0.24->catboost) (2025.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (4.57.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (24.2)\n", "Requirement already satisfied: pillow>=8 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (3.2.3)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from matplotlib->catboost) (6.5.2)\n", "Collecting narwhals>=1.15.1 (from plotly->catboost)\n", "  Downloading narwhals-2.0.1-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: zipp>=3.1.0 in c:\\users\\<USER>\\miniconda3\\envs\\notebook\\lib\\site-packages (from importlib-resources>=3.2.0->matplotlib->catboost) (3.21.0)\n", "Downloading catboost-1.2.8-cp39-cp39-win_amd64.whl (102.5 MB)\n", "   ---------------------------------------- 0.0/102.5 MB ? eta -:--:--\n", "   ---------------------------------------- 0.3/102.5 MB ? eta -:--:--\n", "    --------------------------------------- 1.6/102.5 MB 6.0 MB/s eta 0:00:17\n", "   - -------------------------------------- 2.9/102.5 MB 7.3 MB/s eta 0:00:14\n", "   - -------------------------------------- 4.2/102.5 MB 6.5 MB/s eta 0:00:16\n", "   -- ------------------------------------- 5.8/102.5 MB 6.5 MB/s eta 0:00:15\n", "   -- ------------------------------------- 6.8/102.5 MB 6.5 MB/s eta 0:00:15\n", "   -- ------------------------------------- 7.6/102.5 MB 5.9 MB/s eta 0:00:16\n", "   --- ------------------------------------ 8.4/102.5 MB 5.6 MB/s eta 0:00:17\n", "   --- ------------------------------------ 9.2/102.5 MB 5.3 MB/s eta 0:00:18\n", "   --- ------------------------------------ 9.7/102.5 MB 5.1 MB/s eta 0:00:19\n", "   ---- ----------------------------------- 10.5/102.5 MB 5.0 MB/s eta 0:00:19\n", "   ---- ----------------------------------- 11.3/102.5 MB 4.8 MB/s eta 0:00:19\n", "   ---- ----------------------------------- 11.8/102.5 MB 4.7 MB/s eta 0:00:20\n", "   ---- ----------------------------------- 12.3/102.5 MB 4.5 MB/s eta 0:00:20\n", "   ----- ---------------------------------- 13.1/102.5 MB 4.4 MB/s eta 0:00:21\n", "   ----- ---------------------------------- 13.9/102.5 MB 4.3 MB/s eta 0:00:21\n", "   ----- ---------------------------------- 14.4/102.5 MB 4.2 MB/s eta 0:00:21\n", "   ----- ---------------------------------- 15.2/102.5 MB 4.2 MB/s eta 0:00:21\n", "   ------ --------------------------------- 15.7/102.5 MB 4.1 MB/s eta 0:00:22\n", "   ------ --------------------------------- 16.5/102.5 MB 4.1 MB/s eta 0:00:22\n", "   ------ --------------------------------- 17.0/102.5 MB 4.0 MB/s eta 0:00:22\n", "   ------ --------------------------------- 17.8/102.5 MB 4.0 MB/s eta 0:00:22\n", "   ------- -------------------------------- 18.4/102.5 MB 3.9 MB/s eta 0:00:22\n", "   ------- -------------------------------- 19.1/102.5 MB 3.9 MB/s eta 0:00:22\n", "   ------- -------------------------------- 19.7/102.5 MB 3.9 MB/s eta 0:00:22\n", "   ------- -------------------------------- 20.4/102.5 MB 3.8 MB/s eta 0:00:22\n", "   -------- ------------------------------- 21.0/102.5 MB 3.8 MB/s eta 0:00:22\n", "   -------- ------------------------------- 21.5/102.5 MB 3.7 MB/s eta 0:00:22\n", "   -------- ------------------------------- 21.8/102.5 MB 3.7 MB/s eta 0:00:22\n", "   -------- ------------------------------- 22.3/102.5 MB 3.6 MB/s eta 0:00:23\n", "   -------- ------------------------------- 22.5/102.5 MB 3.6 MB/s eta 0:00:23\n", "   --------- ------------------------------ 23.1/102.5 MB 3.5 MB/s eta 0:00:23\n", "   --------- ------------------------------ 23.3/102.5 MB 3.4 MB/s eta 0:00:23\n", "   --------- ------------------------------ 23.6/102.5 MB 3.4 MB/s eta 0:00:24\n", "   --------- ------------------------------ 23.9/102.5 MB 3.4 MB/s eta 0:00:24\n", "   --------- ------------------------------ 24.4/102.5 MB 3.3 MB/s eta 0:00:24\n", "   --------- ------------------------------ 24.6/102.5 MB 3.3 MB/s eta 0:00:24\n", "   --------- ------------------------------ 24.9/102.5 MB 3.2 MB/s eta 0:00:24\n", "   --------- ------------------------------ 25.4/102.5 MB 3.2 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 25.7/102.5 MB 3.1 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 26.0/102.5 MB 3.1 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 26.2/102.5 MB 3.1 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 26.7/102.5 MB 3.0 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 27.0/102.5 MB 3.0 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 27.3/102.5 MB 3.0 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 27.5/102.5 MB 2.9 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 28.0/102.5 MB 2.9 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 28.3/102.5 MB 2.9 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 28.8/102.5 MB 2.9 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 29.1/102.5 MB 2.8 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 29.4/102.5 MB 2.8 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 29.9/102.5 MB 2.8 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 30.1/102.5 MB 2.8 MB/s eta 0:00:27\n", "   ----------- ---------------------------- 30.7/102.5 MB 2.8 MB/s eta 0:00:27\n", "   ------------ --------------------------- 30.9/102.5 MB 2.7 MB/s eta 0:00:27\n", "   ------------ --------------------------- 31.5/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------ --------------------------- 31.7/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------ --------------------------- 32.2/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------ --------------------------- 32.8/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------ --------------------------- 33.0/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------- -------------------------- 33.6/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------- -------------------------- 34.1/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------- -------------------------- 34.3/102.5 MB 2.7 MB/s eta 0:00:26\n", "   ------------- -------------------------- 34.9/102.5 MB 2.6 MB/s eta 0:00:26\n", "   ------------- -------------------------- 35.4/102.5 MB 2.6 MB/s eta 0:00:26\n", "   ------------- -------------------------- 35.7/102.5 MB 2.6 MB/s eta 0:00:26\n", "   -------------- ------------------------- 36.2/102.5 MB 2.6 MB/s eta 0:00:26\n", "   -------------- ------------------------- 36.7/102.5 MB 2.6 MB/s eta 0:00:26\n", "   -------------- ------------------------- 37.2/102.5 MB 2.6 MB/s eta 0:00:25\n", "   -------------- ------------------------- 37.7/102.5 MB 2.6 MB/s eta 0:00:25\n", "   -------------- ------------------------- 38.3/102.5 MB 2.6 MB/s eta 0:00:25\n", "   --------------- ------------------------ 38.5/102.5 MB 2.6 MB/s eta 0:00:25\n", "   --------------- ------------------------ 39.1/102.5 MB 2.6 MB/s eta 0:00:25\n", "   --------------- ------------------------ 39.6/102.5 MB 2.6 MB/s eta 0:00:25\n", "   --------------- ------------------------ 40.1/102.5 MB 2.6 MB/s eta 0:00:25\n", "   --------------- ------------------------ 40.6/102.5 MB 2.6 MB/s eta 0:00:24\n", "   ---------------- ----------------------- 41.2/102.5 MB 2.6 MB/s eta 0:00:24\n", "   ---------------- ----------------------- 41.7/102.5 MB 2.6 MB/s eta 0:00:24\n", "   ---------------- ----------------------- 42.2/102.5 MB 2.6 MB/s eta 0:00:24\n", "   ---------------- ----------------------- 43.0/102.5 MB 2.6 MB/s eta 0:00:23\n", "   ---------------- ----------------------- 43.5/102.5 MB 2.6 MB/s eta 0:00:23\n", "   ----------------- ---------------------- 44.0/102.5 MB 2.6 MB/s eta 0:00:23\n", "   ----------------- ---------------------- 44.6/102.5 MB 2.6 MB/s eta 0:00:23\n", "   ----------------- ---------------------- 45.4/102.5 MB 2.6 MB/s eta 0:00:22\n", "   ----------------- ---------------------- 45.9/102.5 MB 2.6 MB/s eta 0:00:22\n", "   ------------------ --------------------- 46.7/102.5 MB 2.6 MB/s eta 0:00:22\n", "   ------------------ --------------------- 47.2/102.5 MB 2.6 MB/s eta 0:00:22\n", "   ------------------ --------------------- 48.0/102.5 MB 2.6 MB/s eta 0:00:21\n", "   ------------------- -------------------- 48.8/102.5 MB 2.6 MB/s eta 0:00:21\n", "   ------------------- -------------------- 49.5/102.5 MB 2.7 MB/s eta 0:00:20\n", "   ------------------- -------------------- 50.3/102.5 MB 2.7 MB/s eta 0:00:20\n", "   -------------------- ------------------- 51.4/102.5 MB 2.7 MB/s eta 0:00:19\n", "   -------------------- ------------------- 52.4/102.5 MB 2.7 MB/s eta 0:00:19\n", "   -------------------- ------------------- 53.5/102.5 MB 2.7 MB/s eta 0:00:18\n", "   --------------------- ------------------ 54.5/102.5 MB 2.8 MB/s eta 0:00:18\n", "   --------------------- ------------------ 55.6/102.5 MB 2.8 MB/s eta 0:00:17\n", "   ---------------------- ----------------- 56.9/102.5 MB 2.8 MB/s eta 0:00:17\n", "   ---------------------- ----------------- 57.9/102.5 MB 2.9 MB/s eta 0:00:16\n", "   ----------------------- ---------------- 59.5/102.5 MB 2.9 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 59.8/102.5 MB 2.9 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 60.6/102.5 MB 2.9 MB/s eta 0:00:15\n", "   ------------------------ --------------- 61.6/102.5 MB 2.9 MB/s eta 0:00:15\n", "   ------------------------ --------------- 62.1/102.5 MB 2.9 MB/s eta 0:00:14\n", "   ------------------------ --------------- 62.9/102.5 MB 2.9 MB/s eta 0:00:14\n", "   ------------------------ --------------- 63.4/102.5 MB 2.9 MB/s eta 0:00:14\n", "   ------------------------ --------------- 64.0/102.5 MB 2.9 MB/s eta 0:00:14\n", "   ------------------------- -------------- 64.5/102.5 MB 2.9 MB/s eta 0:00:14\n", "   ------------------------- -------------- 65.0/102.5 MB 2.9 MB/s eta 0:00:13\n", "   ------------------------- -------------- 65.5/102.5 MB 2.9 MB/s eta 0:00:13\n", "   ------------------------- -------------- 66.3/102.5 MB 2.9 MB/s eta 0:00:13\n", "   -------------------------- ------------- 66.8/102.5 MB 2.9 MB/s eta 0:00:13\n", "   -------------------------- ------------- 67.4/102.5 MB 2.9 MB/s eta 0:00:13\n", "   -------------------------- ------------- 68.2/102.5 MB 2.9 MB/s eta 0:00:12\n", "   -------------------------- ------------- 68.7/102.5 MB 2.9 MB/s eta 0:00:12\n", "   --------------------------- ------------ 69.2/102.5 MB 2.9 MB/s eta 0:00:12\n", "   --------------------------- ------------ 70.0/102.5 MB 2.9 MB/s eta 0:00:12\n", "   --------------------------- ------------ 70.5/102.5 MB 2.9 MB/s eta 0:00:11\n", "   --------------------------- ------------ 71.3/102.5 MB 2.9 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 71.8/102.5 MB 2.9 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 72.6/102.5 MB 2.9 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 73.1/102.5 MB 2.9 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 73.9/102.5 MB 2.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 74.4/102.5 MB 2.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 75.2/102.5 MB 2.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 75.8/102.5 MB 2.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 76.5/102.5 MB 2.9 MB/s eta 0:00:09\n", "   ------------------------------ --------- 77.1/102.5 MB 2.9 MB/s eta 0:00:09\n", "   ------------------------------ --------- 77.9/102.5 MB 2.9 MB/s eta 0:00:09\n", "   ------------------------------ --------- 78.6/102.5 MB 2.9 MB/s eta 0:00:09\n", "   ------------------------------ --------- 79.2/102.5 MB 2.9 MB/s eta 0:00:08\n", "   ------------------------------- -------- 80.0/102.5 MB 2.9 MB/s eta 0:00:08\n", "   ------------------------------- -------- 80.7/102.5 MB 3.0 MB/s eta 0:00:08\n", "   ------------------------------- -------- 81.5/102.5 MB 3.0 MB/s eta 0:00:08\n", "   -------------------------------- ------- 82.3/102.5 MB 3.0 MB/s eta 0:00:07\n", "   -------------------------------- ------- 83.1/102.5 MB 3.0 MB/s eta 0:00:07\n", "   -------------------------------- ------- 83.6/102.5 MB 3.0 MB/s eta 0:00:07\n", "   -------------------------------- ------- 84.4/102.5 MB 3.0 MB/s eta 0:00:07\n", "   --------------------------------- ------ 85.2/102.5 MB 3.0 MB/s eta 0:00:06\n", "   --------------------------------- ------ 86.0/102.5 MB 3.0 MB/s eta 0:00:06\n", "   --------------------------------- ------ 86.8/102.5 MB 3.0 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 87.6/102.5 MB 3.0 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 88.3/102.5 MB 3.0 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 89.1/102.5 MB 3.0 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 89.9/102.5 MB 3.0 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 90.7/102.5 MB 3.0 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 91.5/102.5 MB 3.0 MB/s eta 0:00:04\n", "   ------------------------------------ --- 92.3/102.5 MB 3.0 MB/s eta 0:00:04\n", "   ------------------------------------ --- 93.3/102.5 MB 3.0 MB/s eta 0:00:04\n", "   ------------------------------------ --- 94.1/102.5 MB 3.0 MB/s eta 0:00:03\n", "   ------------------------------------- -- 95.2/102.5 MB 2.9 MB/s eta 0:00:03\n", "   ------------------------------------- -- 95.9/102.5 MB 3.0 MB/s eta 0:00:03\n", "   ------------------------------------- -- 97.0/102.5 MB 3.0 MB/s eta 0:00:02\n", "   -------------------------------------- - 98.0/102.5 MB 3.0 MB/s eta 0:00:02\n", "   -------------------------------------- - 99.1/102.5 MB 3.0 MB/s eta 0:00:02\n", "   -------------------------------------- - 99.9/102.5 MB 3.0 MB/s eta 0:00:01\n", "   ---------------------------------------  100.7/102.5 MB 3.0 MB/s eta 0:00:01\n", "   ---------------------------------------  101.4/102.5 MB 3.0 MB/s eta 0:00:01\n", "   ---------------------------------------  102.0/102.5 MB 3.0 MB/s eta 0:00:01\n", "   ---------------------------------------  102.2/102.5 MB 3.0 MB/s eta 0:00:01\n", "   ---------------------------------------- 102.5/102.5 MB 3.0 MB/s eta 0:00:00\n", "Downloading lightgbm-4.6.0-py3-none-win_amd64.whl (1.5 MB)\n", "   ---------------------------------------- 0.0/1.5 MB ? eta -:--:--\n", "   -------------- ------------------------- 0.5/1.5 MB 3.4 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 1.0/1.5 MB 3.1 MB/s eta 0:00:01\n", "   ---------------------------------------- 1.5/1.5 MB 3.0 MB/s eta 0:00:00\n", "Downloading graphviz-0.21-py3-none-any.whl (47 kB)\n", "Downloading plotly-6.2.0-py3-none-any.whl (9.6 MB)\n", "   ---------------------------------------- 0.0/9.6 MB ? eta -:--:--\n", "   -- ------------------------------------- 0.5/9.6 MB 3.4 MB/s eta 0:00:03\n", "   ----- ---------------------------------- 1.3/9.6 MB 3.2 MB/s eta 0:00:03\n", "   ------- -------------------------------- 1.8/9.6 MB 3.1 MB/s eta 0:00:03\n", "   --------- ------------------------------ 2.4/9.6 MB 3.2 MB/s eta 0:00:03\n", "   ------------- -------------------------- 3.1/9.6 MB 3.2 MB/s eta 0:00:03\n", "   --------------- ------------------------ 3.7/9.6 MB 3.3 MB/s eta 0:00:02\n", "   ------------------ --------------------- 4.5/9.6 MB 3.2 MB/s eta 0:00:02\n", "   -------------------- ------------------- 5.0/9.6 MB 3.2 MB/s eta 0:00:02\n", "   ----------------------- ---------------- 5.8/9.6 MB 3.2 MB/s eta 0:00:02\n", "   --------------------------- ------------ 6.6/9.6 MB 3.2 MB/s eta 0:00:01\n", "   ----------------------------- ---------- 7.1/9.6 MB 3.2 MB/s eta 0:00:01\n", "   -------------------------------- ------- 7.9/9.6 MB 3.2 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 8.7/9.6 MB 3.3 MB/s eta 0:00:01\n", "   -------------------------------------- - 9.2/9.6 MB 3.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 9.6/9.6 MB 3.2 MB/s eta 0:00:00\n", "Downloading narwhals-2.0.1-py3-none-any.whl (385 kB)\n", "Installing collected packages: narwhals, graphviz, plotly, lightgbm, catboost\n", "Successfully installed catboost-1.2.8 graphviz-0.21 lightgbm-4.6.0 narwhals-2.0.1 plotly-6.2.0\n"]}], "source": ["!pip install catboost lightgbm"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.532468Z", "iopub.status.busy": "2025-07-16T15:41:29.532167Z", "iopub.status.idle": "2025-07-16T15:41:29.546618Z", "shell.execute_reply": "2025-07-16T15:41:29.545758Z", "shell.execute_reply.started": "2025-07-16T15:41:29.532446Z"}, "trusted": true}, "outputs": [], "source": ["import pandas as pd\n", "from xgboost import XGBClassifier\n", "from sklearn.model_selection import GridSearchCV\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder, OrdinalEncoder\n", "from xgboost import XGBClassifier\n", "from catboost import CatBoostClassifier\n", "from lightgbm import LGBMClassifier\n", "from sklearn.ensemble import VotingClassifier\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "\n", "X_train = all_data[:ntrain]\n", "X_test = all_data[ntrain:]\n", "X=X_train\n", "y=y_train"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.547787Z", "iopub.status.busy": "2025-07-16T15:41:29.547505Z", "iopub.status.idle": "2025-07-16T15:41:29.562171Z", "shell.execute_reply": "2025-07-16T15:41:29.561350Z", "shell.execute_reply.started": "2025-07-16T15:41:29.547763Z"}, "trusted": true}, "outputs": [], "source": ["class_0 = y_train.sum()\n", "class_1 = len(y_train) - class_0\n", "scale_pos_weight = class_1 / class_0"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.563336Z", "iopub.status.busy": "2025-07-16T15:41:29.563059Z", "iopub.status.idle": "2025-07-16T15:41:29.581611Z", "shell.execute_reply": "2025-07-16T15:41:29.580663Z", "shell.execute_reply.started": "2025-07-16T15:41:29.563310Z"}, "trusted": true}, "outputs": [], "source": ["xgb = XGBClassifier(\n", "    max_depth=4,         \n", "    learning_rate=0.01,   \n", "    n_estimators=1000,    \n", "    subsample=0.8,\n", "    colsample_bytree=0.8,\n", "    random_state=42\n", ")\n", "\n", "cat = CatBoostClassifier(\n", "    iterations=300,\n", "    depth=6,\n", "    learning_rate=0.1,\n", "    class_weights=[scale_pos_weight, 1],\n", "    random_seed=42,\n", "    verbose=0\n", ")\n", "\n", "lgbm = LGBMClassifier(\n", "    num_leaves=31,\n", "    learning_rate=0.1,\n", "    n_estimators=300,\n", "    subsample=0.8,\n", "    colsample_bytree=0.8,\n", "    class_weight={0: scale_pos_weight, 1: 1},\n", "    random_state=42\n", ")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.583436Z", "iopub.status.busy": "2025-07-16T15:41:29.582611Z", "iopub.status.idle": "2025-07-16T15:41:29.600148Z", "shell.execute_reply": "2025-07-16T15:41:29.599247Z", "shell.execute_reply.started": "2025-07-16T15:41:29.583403Z"}, "trusted": true}, "outputs": [], "source": ["# Create ensemble\n", "ensemble = VotingClassifier(\n", "    estimators=[\n", "        ('xgb', xgb),\n", "        ('cat', cat),\n", "        ('lgbm', lgbm)\n", "    ],\n", "    voting='soft'\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:29.601448Z", "iopub.status.busy": "2025-07-16T15:41:29.601129Z", "iopub.status.idle": "2025-07-16T15:41:32.125480Z", "shell.execute_reply": "2025-07-16T15:41:32.124673Z", "shell.execute_reply.started": "2025-07-16T15:41:29.601428Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Number of positive: 10959, number of negative: 3860\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000906 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 74\n", "[LightGBM] [Info] Number of data points in the train set: 14819, number of used features: 13\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.889634 -> initscore=2.087006\n", "[LightGBM] [Info] Start training from score 2.087006\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>VotingClassifier(estimators=[(&#x27;xgb&#x27;,\n", "                              XGBClassifier(base_score=None, booster=None,\n", "                                            callbacks=None,\n", "                                            colsample_bylevel=None,\n", "                                            colsample_bynode=None,\n", "                                            colsample_bytree=0.8, device=None,\n", "                                            early_stopping_rounds=None,\n", "                                            enable_categorical=False,\n", "                                            eval_metric=None,\n", "                                            feature_types=None, gamma=None,\n", "                                            grow_policy=None,\n", "                                            importance_type=None,\n", "                                            interaction_constraints=None,\n", "                                            learning_rat...\n", "                                            monotone_constraints=None,\n", "                                            multi_strategy=None,\n", "                                            n_estimators=1000, n_jobs=None,\n", "                                            num_parallel_tree=None,\n", "                                            random_state=42, ...)),\n", "                             (&#x27;cat&#x27;,\n", "                              &lt;catboost.core.CatBoostClassifier object at 0x000001FFD45D8280&gt;),\n", "                             (&#x27;lgbm&#x27;,\n", "                              LGBMClassifier(class_weight={0: np.float64(0.35221549018176507),\n", "                                                           1: 1},\n", "                                             colsample_bytree=0.8,\n", "                                             n_estimators=300, random_state=42,\n", "                                             subsample=0.8))],\n", "                 voting=&#x27;soft&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" ><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>VotingClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.ensemble.VotingClassifier.html\">?<span>Documentation for VotingClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>VotingClassifier(estimators=[(&#x27;xgb&#x27;,\n", "                              XGBClassifier(base_score=None, booster=None,\n", "                                            callbacks=None,\n", "                                            colsample_bylevel=None,\n", "                                            colsample_bynode=None,\n", "                                            colsample_bytree=0.8, device=None,\n", "                                            early_stopping_rounds=None,\n", "                                            enable_categorical=False,\n", "                                            eval_metric=None,\n", "                                            feature_types=None, gamma=None,\n", "                                            grow_policy=None,\n", "                                            importance_type=None,\n", "                                            interaction_constraints=None,\n", "                                            learning_rat...\n", "                                            monotone_constraints=None,\n", "                                            multi_strategy=None,\n", "                                            n_estimators=1000, n_jobs=None,\n", "                                            num_parallel_tree=None,\n", "                                            random_state=42, ...)),\n", "                             (&#x27;cat&#x27;,\n", "                              &lt;catboost.core.CatBoostClassifier object at 0x000001FFD45D8280&gt;),\n", "                             (&#x27;lgbm&#x27;,\n", "                              LGBMClassifier(class_weight={0: np.float64(0.35221549018176507),\n", "                                                           1: 1},\n", "                                             colsample_bytree=0.8,\n", "                                             n_estimators=300, random_state=42,\n", "                                             subsample=0.8))],\n", "                 voting=&#x27;soft&#x27;)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><label>xgb</label></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>XGBClassifier</div></div></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=0.8, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              gamma=None, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.01, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=4, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=1000, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)</pre></div> </div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><label>cat</label></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>CatBoostClassifier</div></div></label><div class=\"sk-toggleable__content fitted\"><pre>&lt;catboost.core.CatBoostClassifier object at 0x000001FFD45D8280&gt;</pre></div> </div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><label>lgbm</label></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LGBMClassifier</div></div></label><div class=\"sk-toggleable__content fitted\"><pre>LGBMClassifier(class_weight={0: np.float64(0.35221549018176507), 1: 1},\n", "               colsample_bytree=0.8, n_estimators=300, random_state=42,\n", "               subsample=0.8)</pre></div> </div></div></div></div></div></div></div></div></div>"], "text/plain": ["VotingClassifier(estimators=[('xgb',\n", "                              XGBClassifier(base_score=None, booster=None,\n", "                                            callbacks=None,\n", "                                            colsample_bylevel=None,\n", "                                            colsample_bynode=None,\n", "                                            colsample_bytree=0.8, device=None,\n", "                                            early_stopping_rounds=None,\n", "                                            enable_categorical=False,\n", "                                            eval_metric=None,\n", "                                            feature_types=None, gamma=None,\n", "                                            grow_policy=None,\n", "                                            importance_type=None,\n", "                                            interaction_constraints=None,\n", "                                            learning_rat...\n", "                                            monotone_constraints=None,\n", "                                            multi_strategy=None,\n", "                                            n_estimators=1000, n_jobs=None,\n", "                                            num_parallel_tree=None,\n", "                                            random_state=42, ...)),\n", "                             ('cat',\n", "                              <catboost.core.CatBoostClassifier object at 0x000001FFD45D8280>),\n", "                             ('lgbm',\n", "                              LGBMClassifier(class_weight={0: np.float64(0.35221549018176507),\n", "                                                           1: 1},\n", "                                             colsample_bytree=0.8,\n", "                                             n_estimators=300, random_state=42,\n", "                                             subsample=0.8))],\n", "                 voting='soft')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train with validation split\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n", "ensemble.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:32.126557Z", "iopub.status.busy": "2025-07-16T15:41:32.126260Z", "iopub.status.idle": "2025-07-16T15:41:32.199377Z", "shell.execute_reply": "2025-07-16T15:41:32.198604Z", "shell.execute_reply.started": "2025-07-16T15:41:32.126535Z"}, "trusted": true}, "outputs": [], "source": ["# Optimize threshold\n", "val_probs = ensemble.predict_proba(X_val)[:, 1]\n", "best_threshold = 0.5\n", "best_acc = 0\n", "\n", "for threshold in np.arange(0.4, 0.6, 0.01):\n", "    preds = (val_probs >= threshold).astype(int)\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:53.912397Z", "iopub.status.busy": "2025-07-16T15:41:53.912059Z", "iopub.status.idle": "2025-07-16T15:41:54.042540Z", "shell.execute_reply": "2025-07-16T15:41:54.041737Z", "shell.execute_reply.started": "2025-07-16T15:41:53.912375Z"}, "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      id  Personality\n", "0  18524            1\n", "1  18525            0\n", "2  18526            1\n", "3  18527            1\n", "4  18528            0\n", "Submitted successfully with XGBoost\n"]}], "source": ["test_probs = ensemble.predict_proba(X_test)[:, 1]\n", "test_preds = (test_probs >= best_threshold).astype(int)\n", "\n", "# Create submission\n", "submission = pd.DataFrame({\n", "    'id': test_ID,\n", "    'Personality': test_preds\n", "})\n", "print(submission.head())\n", "submission['Personality'] = submission['Personality'].map({1: 'Extrovert', 0: 'Introvert'})\n", "submission.to_csv('submissio.csv', index=False)\n", "print(\"Submitted successfully with XGBoost\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"execution": {"iopub.execute_input": "2025-07-16T15:41:32.324070Z", "iopub.status.busy": "2025-07-16T15:41:32.323769Z", "iopub.status.idle": "2025-07-16T15:41:32.328718Z", "shell.execute_reply": "2025-07-16T15:41:32.327751Z", "shell.execute_reply.started": "2025-07-16T15:41:32.324045Z"}, "trusted": true}, "outputs": [], "source": ["# import pandas as pd\n", "# from xgboost import XGBClassifier\n", "# from sklearn.model_selection import GridSearchCV\n", "\n", "# X_train = all_data[:ntrain]\n", "# X_test = all_data[ntrain:]\n", "\n", "# xgb_params = {\n", "#     'max_depth': [4],\n", "#     'learning_rate': [0.01],\n", "#     'n_estimators': [1000],\n", "#     'subsample': [0.8],\n", "#     'colsample_bytree': [0.8]\n", "# }\n", "\n", "\n", "# xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n", "\n", "\n", "# xgb_cv = GridSearchCV(xgb_model, xgb_params, cv=5, n_jobs=-1, verbose=1)\n", "# xgb_cv.fit(X_train, y_train)\n", "\n", "\n", "# xgb_pred = xgb_cv.predict(X_test)\n", "\n", "\n", "# kaggle = pd.DataFrame({'id': test_ID, 'Personality': xgb_pred})\n", "# kaggle['Personality'] = kaggle['Personality'].map({1: 'Extrovert', 0: 'Introvert'})\n", "\n", "\n", "# kaggle.to_csv('submission.csv', index=False)\n", "# print(\"Submitted successfully with XGBoost\")\n"]}], "metadata": {"kaggle": {"accelerator": "none", "dataSources": [{"databundleVersionId": 12738969, "sourceId": 91718, "sourceType": "competition"}, {"datasetId": 7777099, "sourceId": 12336995, "sourceType": "datasetVersion"}], "dockerImageVersionId": 31040, "isGpuEnabled": false, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "notebook", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}