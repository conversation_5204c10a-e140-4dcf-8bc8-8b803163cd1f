#!/usr/bin/env python3
"""
FAST IMPROVED PERSONALITY PREDICTION MODEL
Target: Move from 4th place (0.976518) to top 3 
Quick implementation with key improvements
"""

import numpy as np
import pandas as pd
import warnings
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler, PolynomialFeatures
from sklearn.impute import KNNImputer
from sklearn.ensemble import StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
from sklearn.calibration import CalibratedClassifierCV
from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

warnings.filterwarnings('ignore')

class FastPersonalityPredictor:
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        print("=== FAST IMPROVED PERSONALITY PREDICTION MODEL ===")
        print("Target: Improve from 4th place (0.976518) to top 3")
        print("Key improvements: Advanced features + Stacking + Calibration")
        print("=" * 55)
    
    def load_and_prepare_data(self):
        """Load and prepare data with advanced feature engineering."""
        print("Loading data...")
        
        # Load datasets
        train_df = pd.read_csv("train.csv")
        test_df = pd.read_csv("test.csv")
        
        try:
            dataset_df = pd.read_csv("personality_dataset.csv")
        except FileNotFoundError:
            dataset_df = pd.read_csv("personality_datasert.csv")
        
        # Prepare for merging
        dataset_df = dataset_df.rename(columns={'Personality': 'match_p'}).drop_duplicates()
        
        # Merge
        merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency']
        
        train_df = train_df.merge(dataset_df, how='left', on=merge_cols)
        test_df = test_df.merge(dataset_df, how='left', on=merge_cols)
        
        print(f"Train shape: {train_df.shape}, Test shape: {test_df.shape}")
        return train_df, test_df
    
    def engineer_features(self, train_df, test_df):
        """Advanced feature engineering pipeline."""
        print("Engineering features...")
        
        # Store target and IDs
        y_train = train_df['Personality'].copy()
        test_ids = test_df['id'].copy()
        
        # Combine for consistent feature engineering
        train_fe = train_df.drop(['Personality'], axis=1)
        combined_df = pd.concat([train_fe, test_df], ignore_index=True)
        
        # Advanced missing value imputation
        print("- Advanced imputation...")
        numerical_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside',
                         'Friends_circle_size', 'Post_frequency']
        
        # Create missingness indicators
        for col in numerical_cols:
            if col in combined_df.columns:
                combined_df[f'{col}_missing'] = combined_df[col].isnull().astype(int)
        
        # KNN imputation
        available_num_cols = [col for col in numerical_cols if col in combined_df.columns]
        if available_num_cols:
            knn_imputer = KNNImputer(n_neighbors=5)
            combined_df[available_num_cols] = knn_imputer.fit_transform(combined_df[available_num_cols])
        
        # Handle categorical missing values
        categorical_cols = ['Stage_fear', 'Drained_after_socializing']
        for col in categorical_cols:
            if col in combined_df.columns:
                combined_df[col] = combined_df[col].fillna('Unknown')
        
        # Create interaction features
        print("- Creating interaction features...")
        
        # Social-to-alone behavioral ratios
        combined_df['social_to_alone_ratio'] = (
            combined_df['Social_event_attendance'] / (combined_df['Time_spent_Alone'] + 1e-6)
        )
        combined_df['going_out_to_alone_ratio'] = (
            combined_df['Going_outside'] / (combined_df['Time_spent_Alone'] + 1e-6)
        )
        
        # Friends per post frequency ratios
        combined_df['friends_per_post_ratio'] = (
            combined_df['Friends_circle_size'] / (combined_df['Post_frequency'] + 1e-6)
        )
        
        # Composite social intensity scores
        combined_df['social_intensity_score'] = (
            combined_df['Social_event_attendance'] + combined_df['Going_outside'] + 
            combined_df['Friends_circle_size'] + combined_df['Post_frequency']
        ) / 4
        
        # Isolation tendency metrics
        combined_df['isolation_tendency'] = (
            combined_df['Time_spent_Alone'] / (combined_df['social_intensity_score'] + 1e-6)
        )
        
        # Polynomial interaction features
        print("- Creating polynomial features...")
        poly_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside',
                    'Friends_circle_size', 'Post_frequency']
        available_poly_cols = [col for col in poly_cols if col in combined_df.columns]
        
        if len(available_poly_cols) >= 2:
            poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
            poly_features = poly.fit_transform(combined_df[available_poly_cols])
            poly_names = poly.get_feature_names_out(available_poly_cols)
            
            # Add only interaction terms
            for i, name in enumerate(poly_names):
                if ' ' in name:  # Interaction terms contain spaces
                    clean_name = name.replace(' ', '_x_')
                    combined_df[f'poly_{clean_name}'] = poly_features[:, i]
        
        # Handle categorical encoding
        for col in categorical_cols:
            if col in combined_df.columns:
                dummies = pd.get_dummies(combined_df[col], prefix=col, drop_first=True)
                combined_df = pd.concat([combined_df, dummies], axis=1)
                combined_df = combined_df.drop(col, axis=1)
        
        # Remove ID and non-numeric columns
        if 'id' in combined_df.columns:
            combined_df = combined_df.drop('id', axis=1)
        
        for col in combined_df.columns:
            if combined_df[col].dtype == 'object':
                combined_df = combined_df.drop(col, axis=1)
        
        # Split back
        n_train = len(train_fe)
        X_train = combined_df.iloc[:n_train].copy()
        X_test = combined_df.iloc[n_train:].copy()
        
        print(f"Final feature count: {X_train.shape[1]}")
        return X_train, X_test, y_train, test_ids
    
    def train_models(self, X_train, y_train):
        """Train base models with good default parameters."""
        print("Training models...")
        
        # Encode target and scale features
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_train)
        
        # Base models with optimized parameters
        models = {
            'xgb': XGBClassifier(
                n_estimators=300,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=self.random_state,
                eval_metric='logloss'
            ),
            'lgb': LGBMClassifier(
                n_estimators=300,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=self.random_state,
                objective='binary',
                metric='binary_logloss',
                verbose=-1
            ),
            'cat': CatBoostClassifier(
                iterations=300,
                depth=6,
                learning_rate=0.1,
                random_state=self.random_state,
                verbose=False
            )
        }
        
        # Train base models
        for name, model in models.items():
            print(f"- Training {name}...")
            model.fit(X_scaled, y_encoded)
        
        # Create stacking ensemble
        print("- Creating stacking ensemble...")
        estimators = [(name, model) for name, model in models.items()]
        
        stacking_clf = StackingClassifier(
            estimators=estimators,
            final_estimator=LogisticRegression(random_state=self.random_state),
            cv=5,
            stack_method='predict_proba'
        )
        stacking_clf.fit(X_scaled, y_encoded)
        models['stacking'] = stacking_clf
        
        # Apply calibration
        print("- Applying model calibration...")
        for name, model in list(models.items()):
            if name != 'stacking':
                calibrated = CalibratedClassifierCV(model, method='isotonic', cv=3)
                calibrated.fit(X_scaled, y_encoded)
                models[f'{name}_calibrated'] = calibrated
        
        return models
    
    def evaluate_and_predict(self, models, X_train, y_train, X_test, test_ids):
        """Evaluate models and generate predictions."""
        print("Evaluating models...")
        
        y_encoded = self.label_encoder.transform(y_train)
        X_scaled = self.scaler.transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Cross-validation
        cv_scores = {}
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        
        for name, model in models.items():
            scores = cross_val_score(model, X_scaled, y_encoded, cv=cv, scoring='accuracy')
            cv_scores[name] = scores.mean()
            print(f"- {name}: {scores.mean():.6f} (+/- {scores.std()*2:.6f})")
        
        # Use best model for predictions
        best_model_name = max(cv_scores.keys(), key=lambda x: cv_scores[x])
        best_model = models[best_model_name]
        
        print(f"Best model: {best_model_name} (CV: {cv_scores[best_model_name]:.6f})")
        
        # Generate predictions
        predictions = best_model.predict(X_test_scaled)
        predictions_labels = self.label_encoder.inverse_transform(predictions)
        
        # Create submission
        submission_df = pd.DataFrame({
            'id': test_ids,
            'Personality': predictions_labels
        })
        
        return submission_df, cv_scores

def main():
    """Main execution function."""
    predictor = FastPersonalityPredictor()
    
    # Load and prepare data
    train_df, test_df = predictor.load_and_prepare_data()
    
    # Engineer features
    X_train, X_test, y_train, test_ids = predictor.engineer_features(train_df, test_df)
    
    # Train models
    models = predictor.train_models(X_train, y_train)
    
    # Evaluate and predict
    submission_df, cv_scores = predictor.evaluate_and_predict(models, X_train, y_train, X_test, test_ids)
    
    # Save submission
    submission_df.to_csv('fast_improved_submission.csv', index=False)
    
    print("\n" + "="*55)
    print("FAST IMPROVED MODEL COMPLETED!")
    print(f"Submission saved: fast_improved_submission.csv")
    print(f"Best CV score: {max(cv_scores.values()):.6f}")
    print("Key improvements implemented:")
    print("✓ Advanced feature engineering (interaction + polynomial)")
    print("✓ KNN imputation + missingness indicators") 
    print("✓ StackingClassifier ensemble")
    print("✓ Model calibration")
    print("✓ Cross-validation optimization")
    print("="*55)
    
    return submission_df

if __name__ == "__main__":
    submission = main()
    print("\n🎯 READY FOR SUBMISSION!")
    print("Expected improvement: +0.002 to +0.005 accuracy")
