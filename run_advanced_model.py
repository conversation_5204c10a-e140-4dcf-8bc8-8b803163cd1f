#!/usr/bin/env python3
"""
Simple execution script for the Advanced Personality Prediction Model
Run this script to execute the complete pipeline and generate submission.
"""

import sys
import os
from pathlib import Path

def check_requirements():
    """Check if required files exist."""
    required_files = ['train.csv', 'test.csv', 'personality_datasert.csv']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   • {file}")
        print("\nPlease ensure all data files are in the current directory.")
        return False
    
    return True

def install_requirements():
    """Install required packages."""
    print("📦 Installing required packages...")
    os.system("pip install -r requirements.txt")

def main():
    """Main execution function."""
    print("🚀 Advanced Personality Prediction Model")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return
    
    # Ask user if they want to install requirements
    install = input("Install/update required packages? (y/n): ").lower().strip()
    if install in ['y', 'yes']:
        install_requirements()
    
    # Import and run the model
    try:
        print("\n🔄 Loading advanced model...")
        from advanced_competition_solution import main as run_model
        
        print("🎯 Starting advanced prediction pipeline...")
        submission = run_model()
        
        print("\n✅ Model execution completed successfully!")
        print("📁 Check 'advanced_competition_submission.csv' for results")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all required packages are installed.")
        print("Run: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Execution error: {e}")
        print("Please check the error details above.")

if __name__ == "__main__":
    main()
